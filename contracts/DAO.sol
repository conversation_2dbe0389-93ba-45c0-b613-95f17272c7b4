//SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

// Import Hardhat's console for debugging (remove in production)
import "hardhat/console.sol";
// Import our custom Token contract
import "./Token.sol";

/**
 * @title DAO (Decentralized Autonomous Organization)
 * @dev A smart contract that allows token holders to create and vote on proposals
 * @notice This contract implements a basic DAO where token holders can:
 *         - Create proposals requesting funds
 *         - Vote on proposals (For/Against/Abstain)
 *         - Finalize proposals that meet quorum requirements
 *         - Cancel proposals that are rejected by the community
 */
contract DAO {
    // Contract state variables
    address owner; // Address of the contract deployer (has special privileges)
    Token public token; // Reference to the ERC-20 token contract used for voting power
    uint256 public quorum; // Minimum voting power required to finalize a proposal

    /**
     * @dev Structure to represent a governance proposal
     * @notice Each proposal contains all information needed for community voting
     */
    struct Proposal {
        uint256 id;                    // Unique identifier for the proposal
        string name;                   // Short name/title of the proposal
        string description;            // Detailed description of what the proposal does
        uint256 amount;                // Amount of ETH requested (in Wei)
        address payable recipient;     // Address that will receive funds if proposal passes
        int256 votes;                  // Net votes (positive - negative votes)
        uint256 positiveVotes;         // Total voting power that voted "For"
        uint256 negativeVotes;         // Total voting power that voted "Against"
        uint256 abstainVotes;          // Total voting power that abstained
        uint256 totalParticipation;    // Total voting power that participated
        uint256 deadline;              // Unix timestamp for voting deadline (0 = no deadline)
        bool finalized;                // True if proposal has been executed
        bool cancelled;                // True if proposal has been cancelled
    }

    // Remember: Mapping allows us to store data on the blockchain by key-value pair relationships
    uint256 public proposalCount; // total number of proposals created
    mapping(uint256 => Proposal) public proposals; // mapping to store proposals by their id

    mapping(address => mapping(uint256 => int8)) votes; // mapping to track if an address has voted on a proposal (1 = for, -1 = against, 2 = abstain, 0 = not voted)
    // Note mapping inside mapping

    /* Events stream data to the blockchain, so we can listen to them in our frontend
        Events are used to log important actions in the contract
        They are emitted when certain actions occur, like creating a proposal or voting
        Events are important for frontend applications to listen to and update the UI accordingly
        'Propose' event is emitted when a new proposal is created
        'Vote' event is emitted when an investor votes on a proposal
        'Finalize' event is emitted when a proposal is finalized and funds are transferred
        Event stream can fetch previous events from the blockchain, so we can see the history of actions*/
    event Propose(
        uint id,
        string name,
        string description,
        uint256 amount,
        address recipient,
        address creator
    );
    event Vote(uint256 id, address investor, int8 choice); // 1 = for, -1 = against, 2 = abstain
    event Finalize(uint256 id);
    event Cancel(uint256 id);

    constructor(Token _token, uint256 _quorum) {
        owner = msg.sender;
        token = _token;
        quorum = _quorum;
    }

    // "Receive" function is neccessary to allow contract to receive ether; even if empty
    receive() external payable {} // must be 'external' to receive ether

    /* Must be a DAO token holder to create a proposal or vote
        'onlyInvestor' is a modifier that checks if the sender is a token holder
        'require' is used to check conditions, if condition fails, it reverts the transaction*/
    modifier onlyInvestor() {
        require(
            token.balanceOf(msg.sender) > 0,
            "must be token holder"
        );
        _; // execute the function body after the modifier checks
    }

    // Create proposal
    /**
     * @dev Creates a new proposal for the DAO to vote on
     * @param _name Short title for the proposal
     * @param _description Detailed explanation of the proposal
     * @param _amount Amount of ETH requested (in Wei)
     * @param _recipient Address that will receive the funds
     * @param _deadline Unix timestamp for voting deadline (0 = no deadline)
     * @notice Only token holders can create proposals
     * @notice The DAO contract must have sufficient ETH balance to fund the proposal
     */
    function createProposal(
        string memory _name,
        string memory _description,
        uint256 _amount,
        address payable _recipient,
        uint256 _deadline
    ) external onlyInvestor {
        // Validation checks to ensure proposal is valid
        require(address(this).balance >= _amount, "insufficient contract balance");
        require(_amount > 0, "amount must be greater than 0");
        require(_recipient != address(0), "recipient cannot be zero address");
        require(bytes(_name).length > 0, "proposal name cannot be empty");
        require(bytes(_description).length > 0, "proposal description cannot be empty");
        require(_deadline == 0 || _deadline > block.timestamp, "deadline must be in the future or zero for no deadline");

        // Increment proposal counter to get unique ID
        proposalCount++;

        // Store the new proposal in the mapping
        // Using storage reference to avoid stack depth issues
        Proposal storage newProposal = proposals[proposalCount];
        newProposal.id = proposalCount;
        newProposal.name = _name;
        newProposal.description = _description;
        newProposal.amount = _amount;
        newProposal.recipient = _recipient;
        newProposal.votes = 0;
        newProposal.positiveVotes = 0;
        newProposal.negativeVotes = 0;
        newProposal.abstainVotes = 0;
        newProposal.totalParticipation = 0;
        newProposal.deadline = _deadline;
        newProposal.finalized = false;
        newProposal.cancelled = false;

        emit Propose(
            proposalCount,
            _name,
            _description,
            _amount,
            _recipient,
            msg.sender
        );
    }

    // Vote on proposal with choice (1 = for, -1 = against, 2 = abstain)
    function vote(uint256 _id, int8 _choice) external onlyInvestor {
        _voteWithChoice(_id, _choice);
    }
    
    // Vote on proposal (either in favor or against) - for backward compatibility
    function vote(uint256 _id, bool _inFavor) external onlyInvestor {
        _voteWithChoice(_id, _inFavor ? int8(1) : int8(-1));
    }
    
    // Legacy vote function (always votes in favor) for backward compatibility
    function vote(uint256 _id) external onlyInvestor {
        _voteWithChoice(_id, int8(1));
    }
    
    // Internal function to handle voting logic
    function _voteWithChoice(uint256 _id, int8 _choice) internal {
        // Fetch proposal from mapping by id
        Proposal storage proposal = proposals[_id];

        // Don't let investors vote twice
        require(votes[msg.sender][_id] == 0, "already voted");
        require(_choice == 1 || _choice == -1 || _choice == 2, "invalid choice");

        // Check if voting deadline has passed (if deadline is set)
        require(proposal.deadline == 0 || block.timestamp <= proposal.deadline, "voting deadline has passed");

        // Get voter's token balance
        uint256 voterBalance = token.balanceOf(msg.sender);
        
        // Update total participation
        proposal.totalParticipation += voterBalance;
        
        if (_choice == 1) {
            // Vote in favor
            proposal.positiveVotes += voterBalance;
            proposal.votes += int256(voterBalance);
        } else if (_choice == -1) {
            // Vote against
            proposal.negativeVotes += voterBalance;
            proposal.votes -= int256(voterBalance);
        } else {
            // Abstain - only counts toward participation
            proposal.abstainVotes += voterBalance;
        }
        
        votes[msg.sender][_id] = _choice;

        // Emit an event
        emit Vote(_id, msg.sender, _choice);
    }

    // Finalize proposal & tranfer funds
    function finalizeProposal(uint256 _id) external onlyInvestor {
        // Fetch proposal from mapping by id
        Proposal storage proposal = proposals[_id];// 'storage' keyword allows us to modify the proposal in the mapping

        // Ensure proposal is not already finalized or cancelled
        require(proposal.finalized == false, "proposal already finalized");
        require(proposal.cancelled == false, "proposal was cancelled");

        // Mark proposal as finalized
        proposal.finalized = true;

        // Check that proposal has enough net votes
        require(proposal.votes >= int256(quorum), "must reach quorum to finalize proposal");

        // Check that the contract has enough ether
        require(address(this).balance >= proposal.amount);

        // Transfer the funds to recipient
        (bool sent, ) = proposal.recipient.call{value: proposal.amount}("");
        require(sent);

        // Emit event
        emit Finalize(_id);
    }
    
    // Cancel proposal when against votes reach quorum
    function cancelProposal(uint256 _id) external onlyInvestor {
        // Fetch proposal from mapping by id
        Proposal storage proposal = proposals[_id];
        
        // Ensure proposal is not already finalized or cancelled
        require(proposal.finalized == false, "proposal already finalized");
        require(proposal.cancelled == false, "proposal already cancelled");
        
        // Check that against votes have reached quorum
        require(proposal.negativeVotes >= quorum, "against votes must reach quorum to cancel proposal");
        
        // Mark proposal as cancelled
        proposal.cancelled = true;
        
        // Emit event
        emit Cancel(_id);
    }

    // Check if an investor has voted on a specific proposal
    function hasVoted(address _investor, uint256 _id) public view returns (bool) {
        return votes[_investor][_id] != 0;
    }
    
    // Check if an investor has voted in favor of a specific proposal
    function hasVotedInFavor(address _investor, uint256 _id) public view returns (bool) {
        return votes[_investor][_id] == 1;
    }
    
    // Check if an investor has voted against a specific proposal
    function hasVotedAgainst(address _investor, uint256 _id) public view returns (bool) {
        return votes[_investor][_id] == -1;
    }
    
    // Check if an investor has abstained on a specific proposal
    function hasAbstained(address _investor, uint256 _id) public view returns (bool) {
        return votes[_investor][_id] == 2;
    }
    
    // Get voting choice for an investor on a specific proposal
    function getVoteChoice(address _investor, uint256 _id) public view returns (int8) {
        return votes[_investor][_id];
    }
    
    // Get participation rate for a proposal (percentage of total supply that voted)
    function getParticipationRate(uint256 _id) public view returns (uint256) {
        Proposal storage proposal = proposals[_id];
        uint256 totalSupply = token.totalSupply();
        return totalSupply > 0 ? (proposal.totalParticipation * 100) / totalSupply : 0;
    }
}