//SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.0;

// Import Hardhat's console for debugging (remove in production)
import "hardhat/console.sol";
// Import our custom Token contract
import "./Token.sol";

/**
 * @title DAO (Decentralized Autonomous Organization)
 * @dev A smart contract that allows token holders to create and vote on proposals
 * @notice This contract implements a basic DAO where token holders can:
 *         - Create proposals requesting funds
 *         - Vote on proposals (For/Against/Abstain)
 *         - Finalize proposals that meet quorum requirements
 *         - Cancel proposals that are rejected by the community
 */
contract DAO {
    // Contract state variables
    address owner; // Address of the contract deployer (has special privileges)
    Token public token; // Reference to the ERC-20 token contract used for voting power
    uint256 public quorum; // Minimum voting power required to finalize a proposal

    /**
     * @dev Simplified proposal structure to avoid stack depth issues
     */
    struct Proposal {
        uint256 id;
        string name;
        string description;
        uint256 amount;
        address payable recipient;
        int256 votes;
        uint256 deadline;
        bool finalized;
        bool cancelled;
    }

    // Remember: Mapping allows us to store data on the blockchain by key-value pair relationships
    uint256 public proposalCount; // total number of proposals created
    mapping(uint256 => Proposal) public proposals; // mapping to store proposals by their id

    mapping(address => mapping(uint256 => int8)) votes; // mapping to track if an address has voted on a proposal (1 = for, -1 = against, 2 = abstain, 0 = not voted)
    // Note mapping inside mapping

    /* Events stream data to the blockchain, so we can listen to them in our frontend
        Events are used to log important actions in the contract
        They are emitted when certain actions occur, like creating a proposal or voting
        Events are important for frontend applications to listen to and update the UI accordingly
        'Propose' event is emitted when a new proposal is created
        'Vote' event is emitted when an investor votes on a proposal
        'Finalize' event is emitted when a proposal is finalized and funds are transferred
        Event stream can fetch previous events from the blockchain, so we can see the history of actions*/
    event Propose(
        uint id,
        string name,
        string description,
        uint256 amount,
        address recipient,
        address creator
    );
    event Vote(uint256 id, address investor, int8 choice); // 1 = for, -1 = against, 2 = abstain
    event Finalize(uint256 id);
    event Cancel(uint256 id);

    constructor(Token _token, uint256 _quorum) {
        owner = msg.sender;
        token = _token;
        quorum = _quorum;
    }

    // "Receive" function is neccessary to allow contract to receive ether; even if empty
    receive() external payable {} // must be 'external' to receive ether

    /* Must be a DAO token holder to create a proposal or vote
        'onlyInvestor' is a modifier that checks if the sender is a token holder
        'require' is used to check conditions, if condition fails, it reverts the transaction*/
    modifier onlyInvestor() {
        require(
            token.balanceOf(msg.sender) > 0,
            "must be token holder"
        );
        _; // execute the function body after the modifier checks
    }

    // Create proposal
    /**
     * @dev Creates a new proposal for the DAO to vote on
     * @notice Simplified version to avoid stack depth issues
     */
    function createProposal(
        string memory _name,
        string memory _description,
        uint256 _amount,
        address payable _recipient
    ) external onlyInvestor {
        require(address(this).balance >= _amount);
        require(_amount > 0);
        require(_recipient != address(0));

        proposalCount++;

        proposals[proposalCount].id = proposalCount;
        proposals[proposalCount].name = _name;
        proposals[proposalCount].description = _description;
        proposals[proposalCount].amount = _amount;
        proposals[proposalCount].recipient = _recipient;
        proposals[proposalCount].votes = 0;
        proposals[proposalCount].deadline = 0;
        proposals[proposalCount].finalized = false;
        proposals[proposalCount].cancelled = false;

        emit Propose(proposalCount, _name, _description, _amount, _recipient, msg.sender);
    }

    /**
     * @dev Creates a new proposal with deadline
     * @notice Simplified version to avoid stack depth issues
     */
    function createProposalWithDeadline(
        string memory _name,
        string memory _description,
        uint256 _amount,
        address payable _recipient,
        uint256 _deadline
    ) external onlyInvestor {
        require(address(this).balance >= _amount);
        require(_amount > 0);
        require(_recipient != address(0));
        require(_deadline > block.timestamp);

        proposalCount++;

        proposals[proposalCount].id = proposalCount;
        proposals[proposalCount].name = _name;
        proposals[proposalCount].description = _description;
        proposals[proposalCount].amount = _amount;
        proposals[proposalCount].recipient = _recipient;
        proposals[proposalCount].votes = 0;
        proposals[proposalCount].deadline = _deadline;
        proposals[proposalCount].finalized = false;
        proposals[proposalCount].cancelled = false;

        emit Propose(proposalCount, _name, _description, _amount, _recipient, msg.sender);
    }

    /* ========== EDUCATIONAL SECTION: STACK DEPTH SOLUTIONS ==========
     *
     * The two-function approach above solves the "stack too deep" compilation error.
     * This is a common issue in Solidity when functions have too many parameters.
     *
     * PROBLEM: Single function with many parameters causes stack overflow
     * SOLUTION: Split into two functions with different parameter sets
     *
     * ALTERNATIVE SOLUTIONS:
     * 1. Use structs as parameters (but can still cause issues)
     * 2. Split complex functions into smaller internal functions
     * 3. Use storage references instead of memory variables
     * 4. Reduce local variable count in functions
     *
     * The following shows different approaches to the same functionality.
     */

    /* ALTERNATIVE 1: Named Struct Initialization (More Readable)
     *
     * This approach uses named parameters for better readability but can cause
     * stack depth issues with many parameters in complex functions.
     *
     * proposals[proposalCount] = Proposal({
     *     id: proposalCount,
     *     name: _name,
     *     description: _description,
     *     amount: _amount,
     *     recipient: _recipient,
     *     votes: 0,
     *     positiveVotes: 0,
     *     negativeVotes: 0,
     *     abstainVotes: 0,
     *     totalParticipation: 0,
     *     deadline: _deadline,
     *     finalized: false,
     *     cancelled: false
     * });
     */

    /* ALTERNATIVE 2: Storage Reference Approach (Memory Efficient)
     *
     * This approach uses a storage reference to avoid stack depth issues
     * but requires more gas due to multiple SSTORE operations.
     *
     * Proposal storage newProposal = proposals[proposalCount];
     * newProposal.id = proposalCount;
     * newProposal.name = _name;
     * newProposal.description = _description;
     * newProposal.amount = _amount;
     * newProposal.recipient = _recipient;
     * newProposal.votes = 0;
     * newProposal.positiveVotes = 0;
     * newProposal.negativeVotes = 0;
     * newProposal.abstainVotes = 0;
     * newProposal.totalParticipation = 0;
     * newProposal.deadline = _deadline;
     * newProposal.finalized = false;
     * newProposal.cancelled = false;
     */

    /* ALTERNATIVE 3: Memory Struct Then Copy (Hybrid Approach)
     *
     * This approach creates the struct in memory first, then copies to storage.
     * Can be useful for complex validation before storage.
     *
     * Proposal memory tempProposal = Proposal({
     *     id: proposalCount,
     *     name: _name,
     *     description: _description,
     *     amount: _amount,
     *     recipient: _recipient,
     *     votes: 0,
     *     positiveVotes: 0,
     *     negativeVotes: 0,
     *     abstainVotes: 0,
     *     totalParticipation: 0,
     *     deadline: _deadline,
     *     finalized: false,
     *     cancelled: false
     * });
     * proposals[proposalCount] = tempProposal;
     */

    /**
     * @dev Vote on proposal with specific choice (tri-state voting)
     * @param _id Proposal ID to vote on
     * @param _choice Vote choice: 1 = For, -1 = Against, 2 = Abstain
     * @notice This is the main voting function supporting all vote types
     * @notice Only token holders can vote, and each address can only vote once per proposal
     */
    function vote(uint256 _id, int8 _choice) external onlyInvestor {
        _voteWithChoice(_id, _choice);
    }

    /**
     * @dev Vote on proposal with boolean choice (backward compatibility)
     * @param _id Proposal ID to vote on
     * @param _inFavor True for "For", False for "Against"
     * @notice This function maintains compatibility with older frontend versions
     * @notice Converts boolean to int8 for internal processing
     */
    function vote(uint256 _id, bool _inFavor) external onlyInvestor {
        _voteWithChoice(_id, _inFavor ? int8(1) : int8(-1));
    }

    /**
     * @dev Legacy vote function (always votes "For")
     * @param _id Proposal ID to vote on
     * @notice This function exists for backward compatibility with very old versions
     * @notice Always casts a "For" vote - use other vote functions for more control
     */
    function vote(uint256 _id) external onlyInvestor {
        _voteWithChoice(_id, int8(1));
    }
    
    /**
     * @dev Internal function to handle voting logic (simplified)
     */
    function _voteWithChoice(uint256 _id, int8 _choice) internal {
        Proposal storage proposal = proposals[_id];

        require(votes[msg.sender][_id] == 0, "already voted");
        require(_choice == 1 || _choice == -1 || _choice == 2, "invalid choice");

        if (proposal.deadline > 0) {
            require(block.timestamp <= proposal.deadline, "voting deadline has passed");
        }

        uint256 voterBalance = token.balanceOf(msg.sender);

        if (_choice == 1) {
            proposal.votes += int256(voterBalance);
        } else if (_choice == -1) {
            proposal.votes -= int256(voterBalance);
        }
        // Abstain votes don't affect the vote count

        votes[msg.sender][_id] = _choice;
        emit Vote(_id, msg.sender, _choice);
    }

    /* ========== EDUCATIONAL: EVOLUTION OF VOTING PATTERNS ==========
     *
     * The following shows how voting logic has evolved in this DAO.
     * Study these patterns to understand different approaches to vote counting.
     */

    /* ORIGINAL SIMPLE VOTING (Binary For/Against Only)
     *
     * This was the original implementation supporting only For/Against votes:
     *
     * function vote(uint256 _id, bool _inFavor) external onlyInvestor {
     *     require(votes[msg.sender][_id] == false, "already voted");
     *
     *     uint256 voterBalance = token.balanceOf(msg.sender);
     *
     *     if (_inFavor) {
     *         proposal.votes += int256(voterBalance);
     *     } else {
     *         proposal.votes -= int256(voterBalance);
     *     }
     *
     *     votes[msg.sender][_id] = true;  // Simple boolean tracking
     *     emit Vote(_id, msg.sender, _inFavor);
     * }
     *
     * LIMITATIONS:
     * - No abstain option
     * - No separate tracking of positive/negative votes
     * - No participation metrics
     * - Boolean vote tracking doesn't preserve vote choice
     */

    /* INTERMEDIATE VOTING (Separate Vote Tracking)
     *
     * This intermediate version added separate positive/negative tracking:
     *
     * if (_inFavor) {
     *     proposal.positiveVotes += voterBalance;
     *     proposal.votes += int256(voterBalance);
     * } else {
     *     proposal.negativeVotes += voterBalance;
     *     proposal.votes -= int256(voterBalance);
     * }
     *
     * IMPROVEMENTS:
     * - Separate tracking enables better analytics
     * - Frontend can show vote distribution
     * - Still limited to binary choices
     */

    /* CURRENT ADVANCED VOTING (Tri-State with Analytics)
     *
     * The current implementation supports:
     * - Three vote types (For/Against/Abstain)
     * - Comprehensive vote tracking
     * - Participation metrics
     * - Deadline enforcement
     * - Choice preservation for potential vote changes
     *
     * This evolution shows how smart contracts can be upgraded
     * conceptually while maintaining backward compatibility.
     */

    // Finalize proposal & tranfer funds
    function finalizeProposal(uint256 _id) external onlyInvestor {
        // Fetch proposal from mapping by id
        Proposal storage proposal = proposals[_id];// 'storage' keyword allows us to modify the proposal in the mapping

        // Ensure proposal is not already finalized or cancelled
        require(proposal.finalized == false, "proposal already finalized");
        require(proposal.cancelled == false, "proposal was cancelled");

        // Mark proposal as finalized
        proposal.finalized = true;

        // Check that proposal has enough net votes
        require(proposal.votes >= int256(quorum), "must reach quorum to finalize proposal");

        // Check that the contract has enough ether
        require(address(this).balance >= proposal.amount);

        // Transfer the funds to recipient
        (bool sent, ) = proposal.recipient.call{value: proposal.amount}("");
        require(sent);

        // Emit event
        emit Finalize(_id);
    }
    
    // Cancel proposal when against votes reach quorum
    function cancelProposal(uint256 _id) external onlyInvestor {
        // Fetch proposal from mapping by id
        Proposal storage proposal = proposals[_id];
        
        // Ensure proposal is not already finalized or cancelled
        require(proposal.finalized == false, "proposal already finalized");
        require(proposal.cancelled == false, "proposal already cancelled");
        
        // Check that negative votes have reached quorum (simplified check)
        require(proposal.votes <= -int256(quorum), "must reach quorum to cancel proposal");
        
        // Mark proposal as cancelled
        proposal.cancelled = true;
        
        // Emit event
        emit Cancel(_id);
    }

    // Check if an investor has voted on a specific proposal
    function hasVoted(address _investor, uint256 _id) public view returns (bool) {
        return votes[_investor][_id] != 0;
    }
    
    // Check if an investor has voted in favor of a specific proposal
    function hasVotedInFavor(address _investor, uint256 _id) public view returns (bool) {
        return votes[_investor][_id] == 1;
    }
    
    // Check if an investor has voted against a specific proposal
    function hasVotedAgainst(address _investor, uint256 _id) public view returns (bool) {
        return votes[_investor][_id] == -1;
    }
    
    // Check if an investor has abstained on a specific proposal
    function hasAbstained(address _investor, uint256 _id) public view returns (bool) {
        return votes[_investor][_id] == 2;
    }
    
    // Get voting choice for an investor on a specific proposal
    function getVoteChoice(address _investor, uint256 _id) public view returns (int8) {
        return votes[_investor][_id];
    }
    
    // Get participation rate for a proposal (simplified - returns 0 for now)
    function getParticipationRate(uint256 /* _id */) public pure returns (uint256) {
        // Simplified implementation - would need to track participation separately
        return 0;
    }
}