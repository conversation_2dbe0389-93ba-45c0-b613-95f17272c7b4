const { ethers } = require('hardhat')
const config = require('../src/config.json')

async function main() {
  console.log(`Deploying additional test proposals...\n`)

  // Fetch network
  const { chainId } = await ethers.provider.getNetwork()
  console.log(`Using chainId ${chainId}`)

  // Fetch contracts
  const token = await ethers.getContractAt('Token', config[chainId].token.address)
  const dao = await ethers.getContractAt('DAO', config[chainId].dao.address)

  // Fetch accounts
  const accounts = await ethers.getSigners()
  const deployer = accounts[0]
  const investor1 = accounts[1]
  const investor2 = accounts[2]

  // Check token balances (tokens already distributed in seed.js)
  let balance = await token.balanceOf(deployer.address)
  console.log(`Deployer balance: ${ethers.utils.formatEther(balance)} tokens`)

  let investor1Balance = await token.balanceOf(investor1.address)
  console.log(`Investor1 balance: ${ethers.utils.formatEther(investor1Balance)} tokens`)

  let investor2Balance = await token.balanceOf(investor2.address)
  console.log(`Investor2 balance: ${ethers.utils.formatEther(investor2Balance)} tokens\n`)

  console.log(`Creating additional proposals...\n`)

  let transaction

  // Investor1 creates proposal 1
  transaction = await dao.connect(investor1).createProposal(
    'Community Development Fund',
    'Funding for community development initiatives and educational programs',
    ethers.utils.parseEther('100'),
    investor1.address
  )
  await transaction.wait()

  console.log(`Investor1 created proposal 1\n`)

  // Investor2 creates proposal 2 with deadline
  transaction = await dao.connect(investor2).createProposal(
    'Marketing Campaign',
    'Launch a comprehensive marketing campaign to increase DAO awareness',
    ethers.utils.parseEther('50'),
    investor2.address
  )
  await transaction.wait()

  console.log(`Investor2 created proposal 2\n`)

  // Deployer creates proposal 3
  transaction = await dao.connect(deployer).createProposal(
    'Technical Infrastructure',
    'Upgrade technical infrastructure and development tools',
    ethers.utils.parseEther('75'),
    deployer.address
  )
  await transaction.wait()

  console.log(`Deployer created proposal 3\n`)

  // Add some voting activity
  console.log(`Adding voting activity...\n`)

  // Deployer votes on proposal 1
  transaction = await dao.connect(deployer).vote(1, 1) // Vote For
  await transaction.wait()
  console.log(`Deployer voted FOR proposal 1`)

  // Investor1 votes on proposal 2
  transaction = await dao.connect(investor1).vote(2, 1) // Vote For
  await transaction.wait()
  console.log(`Investor1 voted FOR proposal 2`)

  // Investor2 votes on proposal 1
  transaction = await dao.connect(investor2).vote(1, -1) // Vote Against
  await transaction.wait()
  console.log(`Investor2 voted AGAINST proposal 1`)

  // Add some comments
  console.log(`\nAdding comments...\n`)

  transaction = await dao.connect(deployer).addComment(1, "This proposal will greatly benefit our community!")
  await transaction.wait()
  console.log(`Deployer added comment to proposal 1`)

  transaction = await dao.connect(investor1).addComment(2, "Marketing is essential for growth")
  await transaction.wait()
  console.log(`Investor1 added comment to proposal 2`)

  transaction = await dao.connect(investor2).addComment(3, "Infrastructure improvements are crucial")
  await transaction.wait()
  console.log(`Investor2 added comment to proposal 3`)

  console.log(`\nAdditional test proposals deployment complete!`)
  console.log(`\n=== NEXT STEPS ===`)
  console.log(`1. Start the frontend: npm start`)
  console.log(`2. Connect your wallet and interact with the proposals`)
  console.log(`3. Test voting, commenting, and other features`)
  console.log(`4. This completes the additional proposal test sequence`)
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
