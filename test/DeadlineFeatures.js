const { expect } = require('chai');
const { ethers } = require('hardhat');

describe('DAO Deadline Features', () => {
  let token, dao, deployer, investor1, investor2, recipient;
  let proposalCount = 0;

  beforeEach(async () => {
    // Get signers
    [deployer, investor1, investor2, recipient] = await ethers.getSigners();

    // Deploy Token contract
    const Token = await ethers.getContractFactory('Token');
    token = await Token.deploy('Dapp University', 'DAPP', '1000000');

    // Send tokens to investors
    let transaction = await token.connect(deployer).transfer(investor1.address, ethers.utils.parseUnits('200000', 'ether'));
    await transaction.wait();

    transaction = await token.connect(deployer).transfer(investor2.address, ethers.utils.parseUnits('200000', 'ether'));
    await transaction.wait();

    // Deploy DAO contract
    const DAO = await ethers.getContractFactory('DAO');
    dao = await DAO.deploy(token.address, ethers.utils.parseUnits('500000', 'ether'));

    // Send Ether to DAO treasury
    transaction = await deployer.sendTransaction({
      to: dao.address,
      value: ethers.utils.parseEther('1000')
    });
    await transaction.wait();
  });

  describe('Proposal Creation with Deadlines', () => {
    it('Should create proposal with deadline', async () => {
      // Create deadline 1 hour from now
      const deadline = Math.floor(Date.now() / 1000) + 3600;
      
      const transaction = await dao.connect(investor1).createProposalWithDeadline(
        'Test Proposal with Deadline',
        'A proposal to test deadline functionality',
        ethers.utils.parseEther('100'),
        recipient.address,
        deadline
      );
      await transaction.wait();

      proposalCount++;
      const proposal = await dao.proposals(proposalCount);
      
      expect(proposal.name).to.equal('Test Proposal with Deadline');
      expect(proposal.deadline).to.equal(deadline);
    });

    it('Should create proposal without deadline', async () => {
      const transaction = await dao.connect(investor1).createProposal(
        'Test Proposal No Deadline',
        'A proposal without deadline',
        ethers.utils.parseEther('100'),
        recipient.address,
        0 // No deadline
      );
      await transaction.wait();

      proposalCount++;
      const proposal = await dao.proposals(proposalCount);
      
      expect(proposal.name).to.equal('Test Proposal No Deadline');
      expect(proposal.deadline).to.equal(0);
    });

    it('Should reject proposal with past deadline', async () => {
      // Create deadline in the past
      const pastDeadline = Math.floor(Date.now() / 1000) - 3600;
      
      await expect(
        dao.connect(investor1).createProposalWithDeadline(
          'Invalid Proposal',
          'A proposal with past deadline',
          ethers.utils.parseEther('100'),
          recipient.address,
          pastDeadline
        )
      ).to.be.revertedWith('deadline must be in the future or zero for no deadline');
    });
  });

  describe('Voting with Deadlines', () => {
    beforeEach(async () => {
      // Create a proposal with deadline for testing
      const deadline = Math.floor(Date.now() / 1000) + 3600; // 1 hour from now
      
      const transaction = await dao.connect(investor1).createProposalWithDeadline(
        'Deadline Test Proposal',
        'Testing voting with deadline',
        ethers.utils.parseEther('100'),
        recipient.address,
        deadline
      );
      await transaction.wait();
      proposalCount++;
    });

    it('Should allow voting before deadline', async () => {
      const transaction = await dao.connect(investor2)['vote(uint256,int8)'](proposalCount, 1);
      await transaction.wait();

      const hasVoted = await dao.hasVoted(investor2.address, proposalCount);
      expect(hasVoted).to.equal(true);
    });

    it('Should reject voting after deadline', async () => {
      // Create proposal with very short deadline
      const shortDeadline = Math.floor(Date.now() / 1000) + 1; // 1 second from now
      
      const transaction = await dao.connect(investor1).createProposalWithDeadline(
        'Short Deadline Proposal',
        'Testing expired deadline',
        ethers.utils.parseEther('100'),
        recipient.address,
        shortDeadline
      );
      await transaction.wait();
      proposalCount++;

      // Wait for deadline to pass
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Try to vote after deadline
      await expect(
        dao.connect(investor2)['vote(uint256,int8)'](proposalCount, 1)
      ).to.be.revertedWith('voting deadline has passed');
    });

    it('Should allow voting on proposals without deadline', async () => {
      // Create proposal without deadline
      const transaction = await dao.connect(investor1).createProposal(
        'No Deadline Proposal',
        'Testing voting without deadline',
        ethers.utils.parseEther('100'),
        recipient.address,
        0 // No deadline
      );
      await transaction.wait();
      proposalCount++;

      // Should be able to vote anytime
      const voteTransaction = await dao.connect(investor2)['vote(uint256,int8)'](proposalCount, 1);
      await voteTransaction.wait();

      const hasVoted = await dao.hasVoted(investor2.address, proposalCount);
      expect(hasVoted).to.equal(true);
    });
  });

  describe('Enhanced Voting Features', () => {
    beforeEach(async () => {
      const transaction = await dao.connect(investor1).createProposal(
        'Enhanced Voting Test',
        'Testing enhanced voting features',
        ethers.utils.parseEther('100'),
        recipient.address,
        0
      );
      await transaction.wait();
      proposalCount++;
    });

    it('Should track positive votes correctly', async () => {
      await dao.connect(investor1)['vote(uint256,int8)'](proposalCount, 1); // Vote for
      
      const proposal = await dao.proposals(proposalCount);
      expect(proposal.positiveVotes).to.equal(ethers.utils.parseUnits('200000', 'ether'));
    });

    it('Should track negative votes correctly', async () => {
      await dao.connect(investor1)['vote(uint256,int8)'](proposalCount, -1); // Vote against
      
      const proposal = await dao.proposals(proposalCount);
      expect(proposal.negativeVotes).to.equal(ethers.utils.parseUnits('200000', 'ether'));
    });

    it('Should track abstain votes correctly', async () => {
      await dao.connect(investor1)['vote(uint256,int8)'](proposalCount, 2); // Abstain
      
      const proposal = await dao.proposals(proposalCount);
      expect(proposal.abstainVotes).to.equal(ethers.utils.parseUnits('200000', 'ether'));
    });

    it('Should track total participation correctly', async () => {
      await dao.connect(investor1)['vote(uint256,int8)'](proposalCount, 1); // Vote for
      await dao.connect(investor2)['vote(uint256,int8)'](proposalCount, -1); // Vote against
      
      const proposal = await dao.proposals(proposalCount);
      expect(proposal.totalParticipation).to.equal(ethers.utils.parseUnits('400000', 'ether'));
    });
  });

  describe('Proposal Cancellation', () => {
    beforeEach(async () => {
      const transaction = await dao.connect(investor1).createProposal(
        'Cancellation Test',
        'Testing proposal cancellation',
        ethers.utils.parseEther('100'),
        recipient.address,
        0
      );
      await transaction.wait();
      proposalCount++;
    });

    it('Should allow cancellation when negative votes reach quorum', async () => {
      // Both investors vote against (400k tokens > 500k quorum)
      await dao.connect(investor1)['vote(uint256,int8)'](proposalCount, -1);
      await dao.connect(investor2)['vote(uint256,int8)'](proposalCount, -1);
      
      // Cancel the proposal
      await dao.connect(investor1).cancelProposal(proposalCount);
      
      const proposal = await dao.proposals(proposalCount);
      expect(proposal.cancelled).to.equal(true);
    });

    it('Should reject cancellation without sufficient negative votes', async () => {
      // Only one investor votes against (200k tokens < 500k quorum)
      await dao.connect(investor1)['vote(uint256,int8)'](proposalCount, -1);
      
      await expect(
        dao.connect(investor1).cancelProposal(proposalCount)
      ).to.be.revertedWith('must reach quorum to cancel proposal');
    });
  });
});
