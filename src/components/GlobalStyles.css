/* Global Styles for DAO Application */

/* Global body styling for easier on the eyes background */
body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

/* App container with gradient background */
.app-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

/* Content container with soft background */
.content-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  padding: 30px;
  min-height: calc(100vh - 40px);
  width: 100%;
  max-width: 1313px;
}

/* Enhanced card styling for better visual appeal */
.card {
  border: none !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9) !important;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease;
}

/* Table styling improvements */
.table {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Button enhancements */
.btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Alert styling improvements */
.alert {
  border: none !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px);
}

/* Global progress bar enhancements */
.progress {
  border: 1px solid #dee2e6 !important;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.1) !important;
}

/* Custom progress bars in tables */
.table-progress-bar {
  border: 1px solid #dee2e6;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
  background-color: #f8f9fa;
}

.mini-progress-bar {
  border: 1px solid #dee2e6;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.05);
  background-color: #f8f9fa;
}

/* Enhanced participation background */
.participation-background {
  background: linear-gradient(90deg, #e3f2fd 0%, #f3e5f5 100%);
  opacity: 0.6;
}

/* Table enhancements */
.proposals-table {
  font-size: 0.9rem;
}

.proposals-table th {
  font-weight: 600;
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

.proposals-table td {
  vertical-align: middle;
  padding: 0.75rem 0.5rem;
}

/* Status badges */
.status-badge {
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
}

/* Action buttons container - narrower for cleaner layout */
.action-buttons {
  min-width: 140px;
  max-width: 160px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

/* Actions column styling */
.actions-column {
  width: 222px !important;
  max-width: 222px !important;
  min-width: 222px !important;
}

/* Votes column styling */
.votes-column {
  width: 180px !important;
  max-width: 200px !important;
  min-width: 160px !important;
}

/* Sticky table header */
.sticky-header {
  position: sticky !important;
  top: 0 !important;
  z-index: 100 !important;
  background-color: white !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.sticky-header th {
  position: sticky !important;
  top: 0 !important;
  background-color: white !important;
  z-index: 100 !important;
  border-bottom: 2px solid #dee2e6 !important;
}

/* Table container for better scrolling */
.table-container {
  max-height: 70vh;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  position: relative;
}

/* Voted badge styling */
.voted-badge {
  background: linear-gradient(45deg, #6c757d, #495057);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-container {
    margin: 10px;
    padding: 20px;
    min-height: calc(100vh - 20px);
  }
  
  .proposals-table {
    font-size: 0.8rem;
  }
  
  .action-buttons {
    min-width: 150px;
  }
}
