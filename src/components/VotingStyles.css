/* Enhanced Voting Interface Styles */
.voting-interface {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.voting-interface .btn {
  transition: all 0.3s ease;
  font-weight: 600;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.voting-interface .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.voting-interface .btn:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* Enhanced voting buttons with better visual feedback */
.vote-btn-for {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  color: white;
  position: relative;
}

.vote-btn-for:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  color: white;
}

.vote-btn-against {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
  border: none;
  color: white;
}

.vote-btn-against:hover {
  background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
  color: white;
}

.vote-btn-abstain {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  border: none;
  color: white;
}

.vote-btn-abstain:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  color: white;
}

/* Loading state for voting buttons */
.vote-btn-loading {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  color: white;
  cursor: not-allowed;
}

/* Pulse animation for active voting */
.vote-btn-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Participation Progress Styles */
.pulse-success {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

.completion-celebration {
  background: linear-gradient(45deg, #28a745, #20c997);
  color: white;
  border-radius: 8px;
  animation: celebrate 1s ease-in-out;
}

@keyframes celebrate {
  0% {
    transform: scale(0.9);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.engagement-prompt {
  border-left: 4px solid #007bff;
  animation: gentle-pulse 6s infinite;
}

@keyframes gentle-pulse {
  0%, 100% {
    opacity: 1;
    border-left-color: #007bff;
  }
  25% {
    border-left-color: #28a745;
  }
  50% {
    opacity: 0.9;
    border-left-color: #ffc107;
  }
  75% {
    border-left-color: #dc3545;
  }
}

.message-transition {
  transition: all 0.5s ease-in-out;
}

.message-fade-in {
  animation: fadeInMessage 0.5s ease-in-out;
}

@keyframes fadeInMessage {
  0% {
    opacity: 0;
    transform: translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced vote progress bars */
.vote-progress-container {
  min-width: 150px;
}

.vote-progress-bar {
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
  background-color: #e9ecef;
  transition: all 0.3s ease;
  border: 1px solid #dee2e6;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.vote-progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Enhanced vote progress bars */
.vote-progress-container {
  min-width: 150px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .voting-interface {
    padding: 1rem;
  }

  .voting-interface .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .participation-progress {
    margin-bottom: 1rem;
  }
}

/* Enhanced vote confirmation feedback */
.vote-success-feedback {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  animation: slideIn 0.3s ease-out;
}

.vote-error-feedback {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced voting button groups */
.voting-button-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.voting-button-group .btn {
  min-width: 80px;
  flex: 1;
  max-width: 111px;
}

/* Specific button margins for better spacing */
.vote-btn-for {
  margin-left: 7px;
}

.vote-btn-against {
  margin-right: 7px;
}

.vote-btn-abstain {
  margin-bottom: 8px;
}

/* Vote count badges */
.vote-count-badge {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Enhanced status indicators */
.proposal-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0,0,0,0.1);
}

.status-icon {
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive adjustments for voting interface */
@media (max-width: 768px) {
  .voting-interface {
    padding: 1rem;
  }

  .voting-interface .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .voting-button-group {
    flex-direction: column;
    gap: 0.75rem;
  }

  .voting-button-group .btn {
    max-width: none;
    width: 100%;
  }

  .participation-progress {
    margin-bottom: 1rem;
  }
}

/* Table container with enhanced sticky headers and proper column sizing */
.table-container {
  height: 80vh;
  overflow-y: auto;
  overflow-x: auto;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  border: 1px solid #dee2e6;
  display: block;
  min-width: 100%;
}

/* Enhanced table layout with proper column widths */
.table-container .table {
  margin-bottom: 0;
  width: 100%;
  display: table;
  table-layout: fixed;
  min-width: 1200px; /* Ensure enough space for all columns */
}

/* Ultra-sticky header with proper column sizing */
.table-container thead th {
  position: -webkit-sticky !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  background: #f8f9fa !important;
  background-color: #f8f9fa !important;
  border-bottom: 2px solid #dee2e6 !important;
  vertical-align: middle !important;
  text-align: center !important;
  padding: 1rem 0.75rem !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
  /* Force layer creation */
  transform: translateZ(0) !important;
  -webkit-transform: translateZ(0) !important;
  will-change: transform !important;
}

/* Specific column widths to prevent overlap - match header exactly */
.table-container thead th:nth-child(1) { width: 50px; min-width: 50px; max-width: 50px; text-align: center; } /* # */
.table-container thead th:nth-child(2) { width: 200px; min-width: 200px; max-width: 200px; text-align: center; } /* Proposal Name */
.table-container thead th:nth-child(3) { width: 88px; min-width: 88px; max-width: 88px; text-align: center; } /* Amount */
.table-container thead th:nth-child(4) { width: 144px; min-width: 144px; max-width: 144px; text-align: center; } /* Recipient */
.table-container thead th:nth-child(5) { width: 111px; min-width: 111px; max-width: 111px; text-align: center; } /* Recipient Balance */
.table-container thead th:nth-child(6) { width: 222px; min-width: 222px; max-width: 222px; text-align: center; } /* Status */
.table-container thead th:nth-child(7) { width: 222px; min-width: 222px; max-width: 222px; text-align: center; } /* Votes */
.table-container thead th:nth-child(8) { width: 244px; min-width: 244px; max-width: 244px; text-align: center; } /* Actions */

/* Apply same widths to table cells with exact matching */
.table-container tbody td:nth-child(1) { width: 50px; min-width: 50px; max-width: 50px; text-align: center; }
.table-container tbody td:nth-child(2) { width: 200px; min-width: 200px; max-width: 200px; text-align: left; padding-left: 8px; }
.table-container tbody td:nth-child(3) { width: 88px; min-width: 88px; max-width: 88px; text-align: center; }
.table-container tbody td:nth-child(4) { width: 144px; min-width: 144px; max-width: 144px; text-align: center; }
.table-container tbody td:nth-child(5) { width: 111px; min-width: 111px; max-width: 111px; text-align: center; }
.table-container tbody td:nth-child(6) { width: 222px; min-width: 222px; max-width: 222px; text-align: center; }
.table-container tbody td:nth-child(7) { width: 222px; min-width: 222px; max-width: 222px; text-align: center; }
.table-container tbody td:nth-child(8) { width: 244px; min-width: 244px; max-width: 244px; text-align: center; }

/* Ensure background covers completely */
.table-container thead th::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f9fa;
  z-index: -1;
  pointer-events: none;
}

/* Browser-specific fixes */
@supports (-webkit-appearance: none) {
  .table-container {
    -webkit-overflow-scrolling: touch;
  }
  .table-container thead th {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* Firefox specific fixes */
@-moz-document url-prefix() {
  .table-container thead th {
    background-attachment: local;
    background-image: linear-gradient(#f8f9fa, #f8f9fa);
  }
}

/* Enhanced sticky header mobile styles */
.table-header-fixed {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .table-header-fixed {
    font-size: 0.8rem;
    border-radius: 6px 6px 0 0;
  }

  .table-header-fixed > div {
    min-width: 800px; /* Ensure header scrolls horizontally */
    padding: 0.75rem 0.5rem;
  }

  .table-header-fixed > div > div {
    font-size: 0.75rem;
    padding: 0 0.25rem;
  }

  .table-container {
    height: 60vh;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table-container .table {
    min-width: 800px; /* Match header width */
  }

  /* Adjust column widths for mobile */
  .table-container thead th:nth-child(1) { width: 50px; min-width: 50px; }
  .table-container thead th:nth-child(2) { width: 200px; min-width: 200px; }
  .table-container thead th:nth-child(3) { width: 88px; min-width: 88px; }
  .table-container thead th:nth-child(4) { width: 144px; min-width: 144px; }
  .table-container thead th:nth-child(5) { width: 111px; min-width: 111px; }
  .table-container thead th:nth-child(6) { width: 222px; min-width: 222px; }
  .table-container thead th:nth-child(7) { width: 222px; min-width: 222px; }
  .table-container thead th:nth-child(8) { width: 244px; min-width: 244px; }

  /* Ultimate table alignment solution - Force exact column widths */
  .table-container {
    overflow-x: auto !important;
    width: 100% !important;
    position: relative !important;
  }

  /* Add scroll instruction */
  .table-container::after {
    content: "← Scroll table horizontally to see all columns →";
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
    white-space: nowrap;
  }

  .table-container table {
    table-layout: fixed !important;
    width: 1281px !important; /* Exact width to match header */
    border-collapse: collapse !important;
    margin: 0 !important;
  }

  .table-container tbody tr {
    display: table-row !important;
  }

  .table-container tbody td {
    padding: 8px 4px !important;
    border: 1px solid #dee2e6 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    box-sizing: border-box !important;
    height: 50px !important;
    vertical-align: middle !important;
  }

  /* Force exact column widths with colgroup */
  .table-container colgroup col:nth-child(1) { width: 50px !important; }
  .table-container colgroup col:nth-child(2) { width: 200px !important; }
  .table-container colgroup col:nth-child(3) { width: 88px !important; }
  .table-container colgroup col:nth-child(4) { width: 144px !important; }
  .table-container colgroup col:nth-child(5) { width: 111px !important; }
  .table-container colgroup col:nth-child(6) { width: 222px !important; }
  .table-container colgroup col:nth-child(7) { width: 222px !important; }
  .table-container colgroup col:nth-child(8) { width: 244px !important; }

  .table-container tbody td:nth-child(1) { width: 50px !important; text-align: center !important; }
  .table-container tbody td:nth-child(2) { width: 200px !important; text-align: left !important; }
  .table-container tbody td:nth-child(3) { width: 88px !important; text-align: center !important; }
  .table-container tbody td:nth-child(4) { width: 144px !important; text-align: left !important; }
  .table-container tbody td:nth-child(5) { width: 111px !important; text-align: center !important; }
  .table-container tbody td:nth-child(6) { width: 222px !important; text-align: center !important; }
  .table-container tbody td:nth-child(7) { width: 222px !important; text-align: center !important; }
  .table-container tbody td:nth-child(8) { width: 244px !important; text-align: center !important; }

  .table-container thead th,
  .table-container tbody td {
    padding: 0.5rem 0.25rem;
    font-size: 0.85rem;
  }
}

/* External header table styling to match main table exactly */
.external-table-header table {
  border-collapse: collapse !important;
  margin: 0 !important;
  padding: 0 !important;
}

.external-table-header th:nth-child(1) { width: 50px !important; min-width: 50px !important; max-width: 50px !important; text-align: center; }
.external-table-header th:nth-child(2) { width: 200px !important; min-width: 200px !important; max-width: 200px !important; text-align: center; }
.external-table-header th:nth-child(3) { width: 88px !important; min-width: 88px !important; max-width: 88px !important; text-align: center; }
.external-table-header th:nth-child(4) { width: 144px !important; min-width: 144px !important; max-width: 144px !important; text-align: center; }
.external-table-header th:nth-child(5) { width: 111px !important; min-width: 111px !important; max-width: 111px !important; text-align: center; }
.external-table-header th:nth-child(6) { width: 222px !important; min-width: 222px !important; max-width: 222px !important; text-align: center; }
.external-table-header th:nth-child(7) { width: 222px !important; min-width: 222px !important; max-width: 222px !important; text-align: center; }
.external-table-header th:nth-child(8) { width: 244px !important; min-width: 244px !important; max-width: 244px !important; text-align: center; }

/* Table progress bars */
.table-progress-bar {
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid #dee2e6;
}