/* Enhanced Voting Interface Styles */
.voting-interface {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.voting-interface .btn {
  transition: all 0.3s ease;
  font-weight: 600;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.voting-interface .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.voting-interface .btn:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* Enhanced voting buttons with better visual feedback */
.vote-btn-for {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  color: white;
  position: relative;
}

.vote-btn-for:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  color: white;
}

.vote-btn-against {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
  border: none;
  color: white;
}

.vote-btn-against:hover {
  background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
  color: white;
}

.vote-btn-abstain {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  border: none;
  color: white;
}

.vote-btn-abstain:hover {
  background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
  color: white;
}

/* Loading state for voting buttons */
.vote-btn-loading {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  color: white;
  cursor: not-allowed;
}

/* Pulse animation for active voting */
.vote-btn-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Participation Progress Styles */
.pulse-success {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0% { 
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); 
  }
  70% { 
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); 
  }
  100% { 
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); 
  }
}

.completion-celebration {
  background: linear-gradient(45deg, #28a745, #20c997);
  color: white;
  border-radius: 8px;
  animation: celebrate 1s ease-in-out;
}

@keyframes celebrate {
  0% { 
    transform: scale(0.9); 
    opacity: 0.7; 
  }
  50% { 
    transform: scale(1.05); 
  }
  100% { 
    transform: scale(1); 
    opacity: 1; 
  }
}

.engagement-prompt {
  border-left: 4px solid #007bff;
  animation: gentle-pulse 6s infinite;
}

@keyframes gentle-pulse {
  0%, 100% { 
    opacity: 1; 
    border-left-color: #007bff;
  }
  25% { 
    border-left-color: #28a745;
  }
  50% { 
    opacity: 0.9;
    border-left-color: #ffc107;
  }
  75% { 
    border-left-color: #dc3545;
  }
}

.message-transition {
  transition: all 0.5s ease-in-out;
}

.message-fade-in {
  animation: fadeInMessage 0.5s ease-in-out;
}

@keyframes fadeInMessage {
  0% { 
    opacity: 0;
    transform: translateY(-5px);
  }
  100% { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced vote progress bars */
.vote-progress-container {
  min-width: 150px;
}

.vote-progress-bar {
  height: 6px;
  border-radius: 3px;
  overflow: hidden;
  background-color: #e9ecef;
  transition: all 0.3s ease;
  border: 1px solid #dee2e6;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.vote-progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Enhanced vote progress bars */
.vote-progress-container {
  min-width: 150px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .voting-interface {
    padding: 1rem;
  }
  
  .voting-interface .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
  
  .participation-progress {
    margin-bottom: 1rem;
  }
}

/* Enhanced vote confirmation feedback */
.vote-success-feedback {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  animation: slideIn 0.3s ease-out;
}

.vote-error-feedback {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced voting button groups */
.voting-button-group {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.voting-button-group .btn {
  min-width: 80px;
  flex: 1;
  max-width: 120px;
}

/* Specific button margins for better spacing */
.vote-btn-for {
  margin-left: 7px;
}

.vote-btn-against {
  margin-right: 7px;
}

.vote-btn-abstain {
  margin-bottom: 8px;
}

/* Vote count badges */
.vote-count-badge {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Enhanced status indicators */
.proposal-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0,0,0,0.1);
}

.status-icon {
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Responsive adjustments for voting interface */
@media (max-width: 768px) {
  .voting-interface {
    padding: 1rem;
  }

  .voting-interface .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .voting-button-group {
    flex-direction: column;
    gap: 0.75rem;
  }

  .voting-button-group .btn {
    max-width: none;
    width: 100%;
  }

  .participation-progress {
    margin-bottom: 1rem;
  }
}