[{"inputs": [{"internalType": "contract Token", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_quorum", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "Cancel", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "Finalize", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "address", "name": "creator", "type": "address"}], "name": "Propose", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "investor", "type": "address"}, {"indexed": false, "internalType": "int8", "name": "choice", "type": "int8"}], "name": "Vote", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "cancelProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_description", "type": "string"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "address payable", "name": "_recipient", "type": "address"}], "name": "createProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "finalizeProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "getParticipationRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "getVoteChoice", "outputs": [{"internalType": "int8", "name": "", "type": "int8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "hasAbstained", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "hasVoted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "hasVotedAgainst", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "hasVotedInFavor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposals", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "recipient", "type": "address"}, {"internalType": "int256", "name": "votes", "type": "int256"}, {"internalType": "uint256", "name": "positiveVotes", "type": "uint256"}, {"internalType": "uint256", "name": "negativeVotes", "type": "uint256"}, {"internalType": "uint256", "name": "abstainVotes", "type": "uint256"}, {"internalType": "uint256", "name": "totalParticipation", "type": "uint256"}, {"internalType": "bool", "name": "finalized", "type": "bool"}, {"internalType": "bool", "name": "cancelled", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quorum", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract Token", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "bool", "name": "_inFavor", "type": "bool"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "int8", "name": "_choice", "type": "int8"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]