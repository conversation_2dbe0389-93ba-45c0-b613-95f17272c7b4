[{"inputs": [{"internalType": "contract Token", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_quorum", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "Cancel", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "proposalId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "author", "type": "address"}, {"indexed": false, "internalType": "string", "name": "text", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "CommentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "Finalize", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "address", "name": "creator", "type": "address"}], "name": "Propose", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "investor", "type": "address"}, {"indexed": false, "internalType": "int8", "name": "choice", "type": "int8"}], "name": "Vote", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_proposalId", "type": "uint256"}, {"internalType": "string", "name": "_text", "type": "string"}], "name": "addComment", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "cancelProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_description", "type": "string"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "address payable", "name": "_recipient", "type": "address"}], "name": "createProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_description", "type": "string"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "address payable", "name": "_recipient", "type": "address"}, {"internalType": "uint256", "name": "_deadline", "type": "uint256"}], "name": "createProposalWithDeadline", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "finalizeProposal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_proposalId", "type": "uint256"}, {"internalType": "uint256", "name": "_commentIndex", "type": "uint256"}], "name": "getComment", "outputs": [{"components": [{"internalType": "address", "name": "author", "type": "address"}, {"internalType": "string", "name": "text", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "internalType": "struct DAO.Comment", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_proposalId", "type": "uint256"}], "name": "getCommentCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "getParticipationRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "getVoteChoice", "outputs": [{"internalType": "int8", "name": "", "type": "int8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "hasAbstained", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "hasVoted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "hasVotedAgainst", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_investor", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "hasVotedInFavor", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposalComments", "outputs": [{"internalType": "address", "name": "author", "type": "address"}, {"internalType": "string", "name": "text", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "proposalId", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proposalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "proposals", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address payable", "name": "recipient", "type": "address"}, {"internalType": "int256", "name": "votes", "type": "int256"}, {"internalType": "uint256", "name": "forVotes", "type": "uint256"}, {"internalType": "uint256", "name": "againstVotes", "type": "uint256"}, {"internalType": "uint256", "name": "abstainVotes", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "bool", "name": "finalized", "type": "bool"}, {"internalType": "bool", "name": "cancelled", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "quorum", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract Token", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalComments", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "bool", "name": "_inFavor", "type": "bool"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "int8", "name": "_choice", "type": "int8"}], "name": "vote", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]